#!/usr/bin/python
# -*- coding:utf-8 -*-

"""
UIAutomator2稳定性测试脚本
用于验证改进后的连接机制是否能有效解决连接问题
"""

import time
import unittest
from script.testcases.randomVideotest import *

class UIAutomatorStabilityTest(unittest.TestCase):
    """UIAutomator2稳定性测试"""
    
    def setUp(self):
        """测试前准备"""
        print("=" * 60)
        print("开始UIAutomator2稳定性测试")
        print("=" * 60)
    
    def test_connection_stability(self):
        """测试连接稳定性"""
        print("测试1: 基本连接稳定性")
        
        # 测试基本连接
        self.assertTrue(verify_device_connection(), "基本连接测试失败")
        print("✓ 基本连接测试通过")
        
        # 测试多次连接
        for i in range(5):
            print(f"连接测试 {i+1}/5")
            try:
                d.info  # 测试UIAutomator2基本功能
                d.screen_on()
                time.sleep(2)
                print(f"✓ 连接测试 {i+1} 通过")
            except Exception as e:
                print(f"✗ 连接测试 {i+1} 失败: {e}")
                self.fail(f"连接稳定性测试失败: {e}")
    
    def test_cleanup_mechanism(self):
        """测试清理机制"""
        print("测试2: 清理机制有效性")
        
        # 测试清理功能
        result = cleanup_uiautomator()
        self.assertTrue(result, "UIAutomator2清理失败")
        print("✓ UIAutomator2清理测试通过")
        
        # 测试端口清理
        cleanup_ports()
        print("✓ 端口清理测试通过")
        
        # 测试强制清理
        result = force_kill_uiautomator_processes()
        self.assertTrue(result, "强制清理失败")
        print("✓ 强制清理测试通过")
    
    def test_reconnection_mechanism(self):
        """测试重连机制"""
        print("测试3: 重连机制有效性")
        
        # 模拟连接断开后重连
        try:
            # 先断开连接（模拟）
            cleanup_uiautomator()
            time.sleep(3)
            
            # 测试重连
            result = reconnect_device(max_retries=2)
            self.assertTrue(result, "重连机制测试失败")
            print("✓ 重连机制测试通过")
            
            # 验证重连后功能正常
            self.assertTrue(verify_device_connection(), "重连后连接验证失败")
            print("✓ 重连后功能验证通过")
            
        except Exception as e:
            print(f"✗ 重连机制测试失败: {e}")
            self.fail(f"重连机制测试失败: {e}")
    
    def test_watchers_setup(self):
        """测试watchers设置"""
        print("测试4: Watchers设置稳定性")
        
        try:
            # 测试watchers设置
            result = setup_watchers()
            self.assertTrue(result, "Watchers设置失败")
            print("✓ Watchers设置测试通过")
            
            # 测试watchers功能
            if d and hasattr(d, 'watcher'):
                d.watcher.stop()
                d.watcher.remove()
                print("✓ Watchers清理测试通过")
            
        except Exception as e:
            print(f"✗ Watchers测试失败: {e}")
            self.fail(f"Watchers测试失败: {e}")
    
    def test_error_recovery(self):
        """测试错误恢复机制"""
        print("测试5: 错误恢复机制")
        
        # 模拟各种错误情况
        error_scenarios = [
            "connection aborted",
            "already registered", 
            "address already in use",
            "connection refused"
        ]
        
        for scenario in error_scenarios:
            print(f"测试错误场景: {scenario}")
            
            # 这里只是测试错误识别逻辑，不实际触发错误
            connection_errors = [
                'connection aborted', 'remote end closed connection',
                'uiautomationservice', 'already registered',
                'address already in use', 'connection refused',
                'uiautomation not connected', 'http request failed'
            ]
            
            is_connection_error = any(err in scenario.lower() for err in connection_errors)
            self.assertTrue(is_connection_error, f"错误识别失败: {scenario}")
            print(f"✓ 错误场景 '{scenario}' 识别正确")
    
    def test_monitoring_system(self):
        """测试监控系统"""
        print("测试6: 监控系统功能")
        
        # 测试监控线程启动
        start_connection_monitor()
        self.assertTrue(_monitoring_active, "监控系统启动失败")
        print("✓ 监控系统启动测试通过")
        
        # 等待一段时间让监控运行
        time.sleep(5)
        
        # 测试监控系统停止
        stop_connection_monitor()
        time.sleep(2)
        self.assertFalse(_monitoring_active, "监控系统停止失败")
        print("✓ 监控系统停止测试通过")
    
    def tearDown(self):
        """测试后清理"""
        print("=" * 60)
        print("UIAutomator2稳定性测试完成")
        print("=" * 60)

def run_stability_test():
    """运行稳定性测试"""
    print("开始运行UIAutomator2稳定性测试...")
    
    # 创建测试套件
    suite = unittest.TestSuite()
    suite.addTest(UIAutomatorStabilityTest('test_connection_stability'))
    suite.addTest(UIAutomatorStabilityTest('test_cleanup_mechanism'))
    suite.addTest(UIAutomatorStabilityTest('test_reconnection_mechanism'))
    suite.addTest(UIAutomatorStabilityTest('test_watchers_setup'))
    suite.addTest(UIAutomatorStabilityTest('test_error_recovery'))
    suite.addTest(UIAutomatorStabilityTest('test_monitoring_system'))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    if result.wasSuccessful():
        print("\n" + "=" * 60)
        print("🎉 所有稳定性测试通过！UIAutomator2连接机制工作正常")
        print("=" * 60)
        return True
    else:
        print("\n" + "=" * 60)
        print("❌ 部分稳定性测试失败，需要进一步检查")
        print(f"失败数量: {len(result.failures)}")
        print(f"错误数量: {len(result.errors)}")
        print("=" * 60)
        return False

if __name__ == '__main__':
    run_stability_test()
