#!/usr/bin/python
# -*- coding:utf-8 -*-

"""
安全UIAutomator2使用示例
展示如何使用安全包装函数避免异常和装饰器干扰
"""

import time
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from script.testcases.randomVideotest import (
    d, safe_find_element, safe_click_element, safe_wait_element, 
    safe_get_text, safe_element_operation
)

def example_original_way():
    """原来的写法 - 会抛异常，触发装饰器"""
    print("=== 原来的写法（会抛异常）===")
    
    try:
        # 查找设备按钮
        if d(resourceId='com.xiaomi.mitv.mediaexplorer:id/dev', text='设备').wait(timeout=20.0):
            d(resourceId='com.xiaomi.mitv.mediaexplorer:id/dev', text='设备').click()
            time.sleep(3)
            print("找到并点击了设备按钮")
            return True
        else:
            print('未找到设备按钮')
            return False
    except Exception as e:
        print(f'查找设备按钮时出错: {e}')
        return False

def example_safe_way_1():
    """安全写法1 - 使用exists()方法"""
    print("=== 安全写法1：使用exists()方法 ===")
    
    # 使用exists()方法，不会抛异常，只返回True/False
    device_button = d(resourceId='com.xiaomi.mitv.mediaexplorer:id/dev', text='设备')
    
    if device_button.exists(timeout=20.0):
        device_button.click()
        time.sleep(3)
        print("找到并点击了设备按钮")
        return True
    else:
        print('未找到设备按钮')
        return False

def example_safe_way_2():
    """安全写法2 - 使用安全包装函数"""
    print("=== 安全写法2：使用安全包装函数 ===")
    
    # 使用安全包装函数
    device_button = d(resourceId='com.xiaomi.mitv.mediaexplorer:id/dev', text='设备')
    
    # 方法1：分步操作
    if safe_find_element(device_button, timeout=20):
        if safe_click_element(device_button):
            time.sleep(3)
            print("找到并点击了设备按钮")
            return True
        else:
            print("找到按钮但点击失败")
            return False
    else:
        print('未找到设备按钮')
        return False

def example_safe_way_3():
    """安全写法3 - 使用通用安全操作包装器"""
    print("=== 安全写法3：使用通用安全操作包装器 ===")
    
    device_button = d(resourceId='com.xiaomi.mitv.mediaexplorer:id/dev', text='设备')
    
    # 使用通用包装器
    def find_and_click():
        if device_button.exists(timeout=20):
            device_button.click()
            return True
        return False
    
    if safe_element_operation(find_and_click):
        time.sleep(3)
        print("找到并点击了设备按钮")
        return True
    else:
        print('操作失败')
        return False

def example_complex_operations():
    """复杂操作示例"""
    print("=== 复杂操作示例 ===")
    
    # 示例1：查找多个可能的元素
    possible_selectors = [
        d(resourceId='com.xiaomi.mitv.mediaexplorer:id/dev', text='设备'),
        d(text='设备'),
        d(textContains='设备'),
        d(resourceId='com.xiaomi.mitv.mediaexplorer:id/dev')
    ]
    
    for i, selector in enumerate(possible_selectors):
        print(f"尝试选择器 {i+1}...")
        if safe_find_element(selector, timeout=5):
            print(f"选择器 {i+1} 找到元素")
            if safe_click_element(selector):
                print("点击成功")
                return True
            else:
                print("点击失败，尝试下一个")
        else:
            print(f"选择器 {i+1} 未找到元素")
    
    print("所有选择器都失败了")
    return False

def example_text_operations():
    """文本操作示例"""
    print("=== 文本操作示例 ===")
    
    # 安全获取文本
    title_element = d(resourceId='com.example.app:id/title')
    title_text = safe_get_text(title_element, timeout=10)
    
    if title_text:
        print(f"获取到标题文本: {title_text}")
        return True
    else:
        print("未能获取标题文本")
        return False

def example_wait_operations():
    """等待操作示例"""
    print("=== 等待操作示例 ===")
    
    # 安全等待元素出现
    loading_element = d(resourceId='com.example.app:id/loading')
    content_element = d(resourceId='com.example.app:id/content')
    
    print("等待加载完成...")
    
    # 等待加载元素消失（通过检查内容元素出现）
    if safe_wait_element(content_element, timeout=30):
        print("内容加载完成")
        return True
    else:
        print("等待超时，内容未加载")
        return False

def main():
    """主函数 - 演示所有用法"""
    print("UIAutomator2安全使用方法演示")
    print("="*50)
    
    # 检查设备连接
    try:
        device_info = d.info
        print(f"设备连接正常: {device_info.get('displayWidth', 'unknown')}x{device_info.get('displayHeight', 'unknown')}")
    except:
        print("设备连接异常，请检查连接")
        return
    
    print("\n演示不同的安全使用方法:")
    
    # 演示各种方法
    methods = [
        ("原来的写法", example_original_way),
        ("安全写法1", example_safe_way_1), 
        ("安全写法2", example_safe_way_2),
        ("安全写法3", example_safe_way_3),
        ("复杂操作", example_complex_operations),
        ("文本操作", example_text_operations),
        ("等待操作", example_wait_operations)
    ]
    
    for method_name, method_func in methods:
        print(f"\n{'-'*30}")
        print(f"演示: {method_name}")
        print(f"{'-'*30}")
        
        try:
            result = method_func()
            print(f"结果: {'成功' if result else '失败'}")
        except Exception as e:
            print(f"演示过程中出错: {e}")
        
        time.sleep(2)  # 间隔时间
    
    print(f"\n{'='*50}")
    print("演示完成")
    
    print("\n总结:")
    print("1. 使用 exists() 方法代替 wait() 可以避免异常")
    print("2. 使用安全包装函数可以统一处理异常")
    print("3. UI元素未找到的错误不会触发重连机制")
    print("4. 推荐使用 exists() 方法，简单直接")

if __name__ == '__main__':
    main()
