# UIAutomator2 连接问题修复说明

## 问题描述

在长时间运行的TV测试中，经常遇到以下UIAutomator2连接问题：

1. **UIAutomationService已注册冲突**
   ```
   UiAutomationService android.accessibilityservice.IAccessibilityServiceClient$Stub$Proxy@xxx already registered!
   ```

2. **端口冲突**
   ```
   cannot bind listener: Address already in use
   ```

3. **连接断开**
   ```
   Connection aborted, Remote end closed connection without response
   HTTP request failed: HTTPConnectionPool(host='127.0.0.1', port=38439): Max retries exceeded
   ```

4. **UIAutomation未连接**
   ```
   UiAutomation not connected, UiAutomation@xxx[id=-1, displayId=0, flags=0]
   ```

## 解决方案

### 1. 增强的清理机制

新的 `cleanup_uiautomator()` 函数提供了更彻底的清理：

- 停止所有UIAutomator相关进程
- 清理accessibility服务
- 清理端口占用
- 重启atx-agent服务
- 清理僵尸进程

### 2. 端口管理

新增 `cleanup_ports()` 函数：
- 清理常见的UIAutomator2端口 (7912, 9008, 38439-38443)
- 杀死占用端口的进程
- 防止端口冲突

### 3. ADB连接管理

新增 `force_restart_adb()` 函数：
- 强制重启ADB服务器
- 重新连接设备
- 解决ADB连接问题

### 4. 网络连接监控

新增网络连接检查和恢复机制：
- `check_network_connection()`: 检查网络状态
- `recover_network_connection()`: 恢复网络连接
- 自动检测和修复网络问题

### 5. 自动监控和恢复

新增后台监控线程：
- 每30秒检查一次设备连接状态
- 连续3次失败后自动尝试恢复
- 后台运行，不影响测试执行

### 6. 改进的重试机制

增强的 `ui_safe` 装饰器：
- 智能识别连接错误类型
- 针对不同错误采用不同的恢复策略
- 最多重试2次，避免无限循环

## 使用方法

### 基本使用

代码已经自动集成到 `randomVideotest.py` 中，无需额外配置。

### 手动控制监控

```python
# 启动监控
start_connection_monitor()

# 停止监控
stop_connection_monitor()
```

### 手动清理和重连

```python
# 彻底清理UIAutomator2服务
cleanup_uiautomator()

# 强制重启ADB
force_restart_adb()

# 重连设备
reconnect_device()
```

## 改进效果

1. **减少连接错误**: 通过彻底清理和端口管理，大幅减少连接冲突
2. **自动恢复**: 后台监控自动检测和修复连接问题
3. **更稳定的测试**: 改进的重试机制提高测试稳定性
4. **更好的错误处理**: 智能识别错误类型，采用针对性的修复策略

## 注意事项

1. **监控线程**: 会在后台运行监控线程，程序退出时会自动清理
2. **重试次数**: 避免设置过多重试次数，防止测试时间过长
3. **网络依赖**: 部分功能需要网络连接，确保设备网络正常
4. **权限要求**: 某些清理操作可能需要root权限

## 故障排除

### 如果仍然遇到连接问题

1. **检查设备网络**: 确保TV设备网络连接正常
2. **检查ADB版本**: 使用较新版本的ADB工具
3. **重启设备**: 必要时重启TV设备
4. **检查防火墙**: 确保防火墙不阻止相关端口

### 日志分析

关注以下关键日志：
- "连接监控: xxx" - 监控线程状态
- "UIAutomator2服务清理完成" - 清理操作状态
- "设备重连成功" - 重连操作状态

## 更新历史

- v1.0: 初始版本，基本的清理和重连机制
- v1.1: 增加端口管理和网络监控
- v1.2: 增加后台监控线程和智能重试机制
