#!/usr/bin/python
# -*- coding:utf-8 -*-

"""
FAIL错误恢复测试脚本
专门测试对"invalid literal for int() with base 16: 'FAIL'"错误的处理能力
"""

import time
import unittest
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from script.testcases.randomVideotest import (
    verify_device_connection, 
    recover_from_fail_error,
    force_reset_device_connection,
    cleanup_uiautomator,
    force_kill_uiautomator_processes,
    reconnect_device,
    d
)

class FailErrorRecoveryTest(unittest.TestCase):
    """FAIL错误恢复测试"""
    
    def setUp(self):
        """测试前准备"""
        print("=" * 60)
        print("开始FAIL错误恢复测试")
        print("=" * 60)
    
    def test_device_connection_verification(self):
        """测试设备连接验证功能"""
        print("测试1: 设备连接验证功能")
        
        # 测试连接验证
        result = verify_device_connection()
        print(f"设备连接验证结果: {result}")
        
        if result:
            print("✓ 设备连接验证测试通过")
        else:
            print("✗ 设备连接验证失败，但这可能是预期的")
    
    def test_fail_error_recovery(self):
        """测试FAIL错误恢复机制"""
        print("测试2: FAIL错误恢复机制")
        
        try:
            # 测试FAIL错误恢复功能
            result = recover_from_fail_error()
            self.assertTrue(result, "FAIL错误恢复机制测试失败")
            print("✓ FAIL错误恢复机制测试通过")
            
            # 等待恢复完成
            time.sleep(5)
            
            # 验证恢复后的连接状态
            connection_result = verify_device_connection()
            if connection_result:
                print("✓ 恢复后连接验证通过")
            else:
                print("⚠ 恢复后连接验证失败，可能需要进一步处理")
                
        except Exception as e:
            print(f"✗ FAIL错误恢复机制测试失败: {e}")
            self.fail(f"FAIL错误恢复机制测试失败: {e}")
    
    def test_force_reset_connection(self):
        """测试强制重置连接功能"""
        print("测试3: 强制重置连接功能")
        
        try:
            # 测试强制重置连接
            result = force_reset_device_connection()
            print(f"强制重置连接结果: {result}")
            
            if result:
                print("✓ 强制重置连接测试通过")
                
                # 验证重置后的连接
                time.sleep(3)
                connection_result = verify_device_connection()
                if connection_result:
                    print("✓ 重置后连接验证通过")
                else:
                    print("⚠ 重置后连接验证失败")
            else:
                print("⚠ 强制重置连接失败，但这可能是预期的")
                
        except Exception as e:
            print(f"✗ 强制重置连接测试失败: {e}")
            # 不让这个测试失败整个测试套件
            print("⚠ 强制重置连接测试异常，但继续执行其他测试")
    
    def test_comprehensive_recovery(self):
        """测试综合恢复流程"""
        print("测试4: 综合恢复流程")
        
        try:
            print("执行完整的恢复流程...")
            
            # 1. 清理UIAutomator
            cleanup_result = cleanup_uiautomator()
            print(f"UIAutomator清理结果: {cleanup_result}")
            
            # 2. 强制清理进程
            force_cleanup_result = force_kill_uiautomator_processes()
            print(f"强制进程清理结果: {force_cleanup_result}")
            
            # 3. FAIL错误恢复
            fail_recovery_result = recover_from_fail_error()
            print(f"FAIL错误恢复结果: {fail_recovery_result}")
            
            # 4. 等待恢复完成
            time.sleep(8)
            
            # 5. 验证最终连接状态
            final_connection = verify_device_connection()
            print(f"最终连接状态: {final_connection}")
            
            if final_connection:
                print("✓ 综合恢复流程测试通过")
            else:
                print("⚠ 综合恢复流程后连接仍有问题，可能需要手动干预")
                
        except Exception as e:
            print(f"✗ 综合恢复流程测试失败: {e}")
            print("⚠ 综合恢复流程异常，但这可能反映了实际的系统状态")
    
    def test_reconnection_mechanism(self):
        """测试重连机制"""
        print("测试5: 重连机制")
        
        try:
            # 测试重连机制
            result = reconnect_device(max_retries=2)
            print(f"重连机制结果: {result}")
            
            if result:
                print("✓ 重连机制测试通过")
                
                # 验证重连后的功能
                connection_result = verify_device_connection()
                if connection_result:
                    print("✓ 重连后功能验证通过")
                else:
                    print("⚠ 重连后功能验证失败")
            else:
                print("⚠ 重连机制失败，这可能反映了当前的系统状态")
                
        except Exception as e:
            print(f"✗ 重连机制测试失败: {e}")
            print("⚠ 重连机制测试异常")
    
    def tearDown(self):
        """测试后清理"""
        print("=" * 60)
        print("FAIL错误恢复测试完成")
        print("=" * 60)

def run_fail_error_recovery_test():
    """运行FAIL错误恢复测试"""
    print("开始运行FAIL错误恢复测试...")
    
    # 创建测试套件
    suite = unittest.TestSuite()
    suite.addTest(FailErrorRecoveryTest('test_device_connection_verification'))
    suite.addTest(FailErrorRecoveryTest('test_fail_error_recovery'))
    suite.addTest(FailErrorRecoveryTest('test_force_reset_connection'))
    suite.addTest(FailErrorRecoveryTest('test_comprehensive_recovery'))
    suite.addTest(FailErrorRecoveryTest('test_reconnection_mechanism'))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果摘要
    print("\n" + "=" * 60)
    print("测试结果摘要:")
    print(f"总测试数: {result.testsRun}")
    print(f"失败数: {len(result.failures)}")
    print(f"错误数: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    print("=" * 60)
    
    return result.wasSuccessful()

if __name__ == '__main__':
    success = run_fail_error_recovery_test()
    sys.exit(0 if success else 1)
