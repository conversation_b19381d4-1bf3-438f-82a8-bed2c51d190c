#!/usr/bin/python
# -*- coding:utf-8 -*-

"""
FAIL错误综合测试和恢复验证脚本
测试新的智能诊断和恢复机制的有效性
"""

import time
import unittest
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from script.testcases.randomVideotest import (
    verify_device_connection, 
    diagnose_connection_issue,
    cleanup_uiautomator,
    force_kill_uiautomator_processes,
    system_level_recovery,
    reconnect_device,
    init_device,
    d
)

class ComprehensiveFAILErrorTest(unittest.TestCase):
    """综合FAIL错误测试类"""
    
    def setUp(self):
        """测试前准备"""
        print("\n" + "="*60)
        print("开始FAIL错误综合测试")
        print("="*60)
    
    def tearDown(self):
        """测试后清理"""
        print("="*60)
        print("测试完成")
        print("="*60)
    
    def test_01_connection_diagnosis(self):
        """测试1: 连接问题诊断功能"""
        print("\n测试1: 连接问题诊断功能")
        
        try:
            # 执行诊断
            issues = diagnose_connection_issue()
            
            print(f"诊断结果: {issues if issues else '未发现问题'}")
            
            # 验证诊断功能正常工作
            self.assertIsInstance(issues, list, "诊断结果应该是列表类型")
            print("✓ 连接诊断功能测试通过")
            
        except Exception as e:
            print(f"✗ 连接诊断功能测试失败: {e}")
            self.fail(f"连接诊断功能测试失败: {e}")
    
    def test_02_intelligent_cleanup(self):
        """测试2: 智能清理机制"""
        print("\n测试2: 智能清理机制")
        
        try:
            print("执行智能清理...")
            result = cleanup_uiautomator()
            
            self.assertTrue(result, "智能清理应该成功")
            print("✓ 智能清理机制测试通过")
            
            # 等待清理完成
            time.sleep(5)
            
            # 验证清理效果
            print("验证清理效果...")
            issues_after_cleanup = diagnose_connection_issue()
            print(f"清理后问题: {issues_after_cleanup if issues_after_cleanup else '无问题'}")
            
        except Exception as e:
            print(f"✗ 智能清理机制测试失败: {e}")
            self.fail(f"智能清理机制测试失败: {e}")
    
    def test_03_connection_verification(self):
        """测试3: 智能连接验证"""
        print("\n测试3: 智能连接验证")
        
        try:
            print("执行连接验证...")
            result = verify_device_connection()
            
            print(f"连接验证结果: {'成功' if result else '失败'}")
            
            if not result:
                print("连接验证失败，这可能反映了实际的连接问题")
                # 尝试诊断问题
                issues = diagnose_connection_issue()
                print(f"诊断到的问题: {issues}")
            else:
                print("✓ 智能连接验证测试通过")
                
        except Exception as e:
            print(f"✗ 智能连接验证测试失败: {e}")
            print("⚠ 这可能反映了实际的系统状态")
    
    def test_04_intelligent_reconnection(self):
        """测试4: 智能重连机制"""
        print("\n测试4: 智能重连机制")
        
        try:
            print("执行智能重连...")
            
            # 先执行清理模拟连接断开
            cleanup_uiautomator()
            time.sleep(3)
            
            # 测试智能重连
            result = reconnect_device(max_retries=2)
            
            if result:
                print("✓ 智能重连机制测试通过")
                
                # 验证重连后功能正常
                connection_ok = verify_device_connection()
                if connection_ok:
                    print("✓ 重连后连接验证通过")
                else:
                    print("⚠ 重连后连接验证失败")
            else:
                print("⚠ 智能重连机制测试失败，但这可能反映了实际的系统状态")
                
        except Exception as e:
            print(f"✗ 智能重连机制测试异常: {e}")
            print("⚠ 这可能反映了实际的系统状态")
    
    def test_05_system_level_recovery(self):
        """测试5: 系统级恢复机制"""
        print("\n测试5: 系统级恢复机制")
        
        try:
            print("执行系统级恢复...")
            print("⚠ 注意: 这是最强力的恢复机制，可能需要较长时间")
            
            result = system_level_recovery()
            
            if result:
                print("✓ 系统级恢复机制测试通过")
                
                # 等待系统稳定
                time.sleep(10)
                
                # 验证恢复效果
                print("验证系统级恢复效果...")
                final_issues = diagnose_connection_issue()
                print(f"恢复后问题: {final_issues if final_issues else '无问题'}")
                
            else:
                print("⚠ 系统级恢复机制测试失败")
                
        except Exception as e:
            print(f"✗ 系统级恢复机制测试异常: {e}")
            print("⚠ 这可能需要手动干预")
    
    def test_06_comprehensive_recovery_flow(self):
        """测试6: 综合恢复流程"""
        print("\n测试6: 综合恢复流程")
        
        try:
            print("执行完整的综合恢复流程...")
            
            # 1. 诊断问题
            print("步骤1: 诊断当前问题")
            initial_issues = diagnose_connection_issue()
            print(f"初始问题: {initial_issues if initial_issues else '无问题'}")
            
            # 2. 智能清理
            print("步骤2: 执行智能清理")
            cleanup_result = cleanup_uiautomator()
            print(f"清理结果: {'成功' if cleanup_result else '失败'}")
            
            # 3. 等待清理完成
            time.sleep(8)
            
            # 4. 验证清理效果
            print("步骤3: 验证清理效果")
            after_cleanup_issues = diagnose_connection_issue()
            print(f"清理后问题: {after_cleanup_issues if after_cleanup_issues else '无问题'}")
            
            # 5. 如果仍有严重问题，执行系统级恢复
            if after_cleanup_issues and any("异常" in issue for issue in after_cleanup_issues):
                print("步骤4: 执行系统级恢复")
                system_recovery_result = system_level_recovery()
                print(f"系统级恢复结果: {'成功' if system_recovery_result else '失败'}")
                time.sleep(10)
            
            # 6. 最终验证
            print("步骤5: 最终验证")
            final_connection = verify_device_connection()
            final_issues = diagnose_connection_issue()
            
            print(f"最终连接状态: {'正常' if final_connection else '异常'}")
            print(f"最终问题列表: {final_issues if final_issues else '无问题'}")
            
            if final_connection:
                print("✓ 综合恢复流程测试通过")
            else:
                print("⚠ 综合恢复流程后仍有问题，可能需要手动干预")
                
        except Exception as e:
            print(f"✗ 综合恢复流程测试异常: {e}")
            print("⚠ 这可能需要进一步的系统级干预")

def run_comprehensive_test():
    """运行综合测试"""
    print("开始运行FAIL错误综合测试...")
    print("这个测试将验证所有新的智能诊断和恢复机制")
    print("测试过程可能需要几分钟时间，请耐心等待...")
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(ComprehensiveFAILErrorTest)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出总结
    print("\n" + "="*60)
    print("测试总结:")
    print(f"总测试数: {result.testsRun}")
    print(f"失败数: {len(result.failures)}")
    print(f"错误数: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    print("="*60)
    
    return result.wasSuccessful()

if __name__ == '__main__':
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)
